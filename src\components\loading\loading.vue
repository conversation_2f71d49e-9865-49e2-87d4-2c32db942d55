<template>
  <div class="loading-container" v-if="!appResized">
    <div class="loading-content">
      <div class="logo-container">
        <img :src="logoSrc" alt="PropVR Logo" class="logo" @error="handleImageError">
        <div v-if="!logoLoaded" class="text-logo">PropVR</div>
      </div>
      <div class="spinner"></div>
      <h2>{{ loadingText }}</h2>
      <div class="loading-status">{{ currentStatus }}</div>
      <div class="network-warning" v-if="showNetworkWarning">
        <i class="iconfont icon-warning"></i>
        <!-- <span>{{ ui.badNetwork }}</span> -->
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import Log from '@/utils/log';

export default {
  name: 'LoadingScreen',
  data() {
    return {
      currentStatus: 'Connecting...',
      showNetworkWarning: false,
      appResized: false,
      logoLoaded: true,
      logoSrc: require('@/assets/images/propvr-logo.svg'),
      loadingStages: {
        'INITED': 'Initializing...',
        'WEBSOCKET_CHANNEL_OPEN': 'Connecting to server...',
        'LOGIN_SUCCESS': 'Logging in...',
        'RTC_CONNECTED': 'Establishing video connection...',
        'MEDIA_LOADED': 'Loading application...',
        'SYNC_APP': 'Syncing remote app, please wait...'
      }
    };
  },
  computed: {
    loadingText() {
      return 'Loading PropVR Experience';
    },
    ...mapState({
      larksr: state => state.larksr,
      ui: state => state.ui
    })
  },
  mounted() {
    this.setupEventListeners();

    // If larksr isn't available yet, watch for changes
    if (!this.larksr) {
      this.$watch('larksr', (newVal) => {
        if (newVal) {
          this.setupEventListeners();
        }
      });
    }
  },

  methods: {
    handleImageError() {
      console.error('Failed to load logo image');
      this.logoLoaded = false;
      // Try loading from a direct URL as fallback
      this.logoSrc = 'https://storagecdn.propvr.ai/WebsiteAssets%2Flogo%2Fpropvr-white-logo.svg?alt=media';
    },
    setupEventListeners() {
      if (!this.larksr) return;

      // Listen for loading status events
      this.larksr.on('status', (e) => {
        Log.info('Loading status:', e);
        if (this.loadingStages[e.message]) {
          this.currentStatus = this.loadingStages[e.message];
        } else {
          this.currentStatus = e.message;
        }
      });

      // Listen for network quality events
      this.larksr.on('netstate', (e) => {
        Log.info('Network state:', e);
        if (e.state === 'poor' || e.state === 'bad') {
          this.showNetworkWarning = true;
        } else {
          this.showNetworkWarning = false;
        }
      });

      // Listen for any error or warning events
      this.larksr.on('error', (e) => {
        Log.error('Error:', e);
        this.currentStatus = e.message || 'An error occurred';
      });

      this.larksr.on('warning', (e) => {
        Log.warn('Warning:', e);
        if (e.message && e.message.includes('network')) {
          this.showNetworkWarning = true;
        }
      });

      // Listen for APP_RESIZE event to hide the loading screen
      this.larksr.on('appresize', (e) => {
        Log.info('LarkSRClientEvent APP_RESIZE', e);
        Log.info('APP_RESIZE event received, hiding loading screen');
        console.log('APP_RESIZE event data:', e);

        // Add a small delay to ensure everything is loaded
        setTimeout(() => {
          this.appResized = true;
          console.log('Loading screen hidden after APP_RESIZE event');
        }, 500);
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0a2463 0%, #3e92cc 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

/* Override the default loading background */
:global(.loading.svelte-ro1nws.svelte-ro1nws) {
  background-image: none !important;
  background: linear-gradient(135deg, #0a2463 0%, #3e92cc 100%) !important;
}

.loading-content {
  text-align: center;
  color: white;
  max-width: 80%;
}

.logo-container {
  margin-bottom: 30px;
  text-align: center;
}

.logo {
  width: 250px;
  height: auto;
  margin: 0 auto 20px;
  display: block;
  filter: brightness(1.5);
  background-color: rgba(255, 255, 255, 0.1);
  padding: 15px;
  border-radius: 8px;
}

.text-logo {
  font-size: 36px;
  font-weight: bold;
  color: white;
  margin-bottom: 20px;
  letter-spacing: 2px;
  text-transform: uppercase;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 15px;
  border-radius: 8px;
  display: inline-block;
}

.spinner {
  width: 60px;
  height: 60px;
  margin: 0 auto 20px;
  border: 5px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

.loading-status {
  margin-top: 10px;
  font-size: 16px;
  color: #e0e0e0;
}

h2 {
  font-size: 24px;
  font-weight: 500;
  margin: 15px 0;
  color: white;
}

.network-warning {
  margin-top: 20px;
  padding: 15px;
  background-color: rgba(255, 255, 255, 0.1);
  border-left: 4px solid #ff9800;
  color: #ffcc80;
  text-align: left;
  display: flex;
  align-items: center;
  border-radius: 4px;
}

.network-warning i {
  margin-right: 10px;
  font-size: 20px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style>
