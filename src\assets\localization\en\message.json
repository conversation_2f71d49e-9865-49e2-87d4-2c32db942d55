{"level": {"INFO": "INFO", "WARING": "WARING", "ERROR": "ERROR"}, "msg": {"CONNECT_WEBRTC": "Video connection...", "ICE_FAILED": "The current network environment is poor. Please close the page and wait a moment to re-enter the application.", "ICE_SUCCESS": "Connection succeeded.", "WEBRTC_HANGUP": "The current network environment is poor. Please close the page and wait a moment to re-enter the application.", "connect CONNECT_WS_ERR": "Connection failure.", "CONNECT_WS_ERR_1": "Client has started.", "CONNECT_WS_CLOSE": "Connection to server is down.", "APP_LOGIN_ERR_1": "Client is not authenticated.", "APP_LOGIN_ERR_2": "Client repeated login.", "CONNECT_APP_ERR": "Connection to the application server failed.", "CONNECT_APP_ERR2": "Connection application server timeout.", "SIGNALING_APP_TIMEOUT": "Connection timed out. Please check the network or contact the administrator.", "REMOTE_APP_FAILED": "Failed to launch cloud app. Please contact the administrator.", "REMOTE_APP_CLOSE": "The cloud app is closed. Please re-enter the course.", "REFRESH": "Reconnecting...", "COURSE_OVER": "Course time.", "COURSE_QUIT": "Confirm to exit the application ?", "ORIENTATION": "Horizontal screen browsing is better.", "REQUEST_LOCK_MOUSE": "For exclusive mouse mode, click the lock mouse button.", "LOCK_MOUSE": "Enter exclusive mouse mode.", "PARAM_ERROR": "Application parameter error.", "LOAD_CONFIG_FAILED": "Failed to read configuration file.", "TOKEN_EXPIRED": "The token has expired, please re-enter.", "REQUEST_PLAY_VIDEO": "Click OK to enter the app.", "LOGIN_SUCESS": "Login successful.", "LOGIN_ERROR_COURSE_NOTATSTART": "<PERSON><PERSON> failed, course has not arrived.", "LOGIN_ERROR_COURSE_NOTEXIST": "<PERSON><PERSON> failed, course does not exist.", "LOGIN_ERROR_COURSE_TIMEOUT": "<PERSON><PERSON> failed, the course has arrived.", "LOGIN_ERROR_MEDIA_NOTREADY": "<PERSON><PERSON> failed, course is not ready, please try again later.", "LOGIN_ERROR_SESSION_EXIST": "Login failed, existing client connection.", "LOGIN_ERROR_VALIDUSER": "<PERSON><PERSON> failed, user authentication failed.", "LOGIN_ERROR_SAFEDOG_NOTFOUND": "Lo<PERSON> failed, the system did not find the dongle", "LOGIN_ERROR_SAFEDOG_TIMEOUT": "Login failed, dongle has expired.", "LOGIN_ERROR_UNKNOWN": "Unknown login result.", "LOGIN_ERROR_EMPTY": "Login result is empty.", "APP_LOGIN_TIMOUT": "Login timeout.", "APP_LOGIN_MESSAGE_ERR": "<PERSON><PERSON> returns an error message.", "CONNECT_TO_APP_SERVER_TIMEOUT": "Connection application server timeout.", "APP_START_STREAM_TIMEOUT": "Start streaming timeout", "LK_LOADING_TIMEOUT": "Loading timeout.", "NO_OPERATION_TIMEOUT": "No operation exceeds the system setting time, close the connection。", "RENDER_SERVER_CONNECTED": "connect to render server success", "RENDER_SERVER_FAILED": "connect to render server failed", "RENDER_SERVER_CLOSE": "connection with render server close", "RENDER_SERVER_ERROR": "connection with render server error", "PROXY_SERVER_CONNECTED": "connect to proxy server success", "PROXY_SERVER_FAILED": "connect to proxy server failed", "PROXY_SERVER_CLOSE": "connection with proxy server close", "PROXY_SERVER_ERROR": "connection with proxy server error", "VERSION_CHECK_SUCCESS": "version check success", "VERSION_CHECK_FAILED": "version check failed", "TASK_SUCCESS": "Server get task success", "TASK_ERROR": "Server get task error", "TASK_NOTFOUND": "Server get task notfound", "TASK_SERVER_ERROR": "Server get task server error", "TASK_APP_WRONGPARAM": "task app wrong param", "TASK_NO_GPU_RESOURCE": "task no gpu resource", "START_SUCCESS": "start success", "START_FAILED": "start failed", "START_STREAM_PROCESS_START_FAILED": "start stream process failed", "START_STREAM_PROCESS_START_TIMEOUT": "start stream process timout", "START_STREAM_NOT_STREAMING": "start stream not streaming", "START_STREAM_ENCODER_ERROR": "start stream encoder error", "RTC_EVENT_PEERCONNECTION_CONNECTED": "RTC connect success", "RTC_EVENT_PEERCONNECTION_CLOSED": "RTC connect close", "RTC_EVENT_PEERCONNECTION_ERROR": "RTC connect error", "VIDEO_LOADED": "video loaded", "CONNECT_TO_ADMIN": "Please connect to adminer", "RTC_RETRY_CONNECTION": "Peerconnection closed，retrying...", "LK_NOTIFY_CLIENT_LOGOUT_PLAYER_LOGOUT": "Sever request logout.", "LK_NOTIFY_CLIENT_LOGOUT_TASKOWNER_LOGOUT": "Host disconnected.", "LK_APP_PROCESS_NOTIFI_APP_QUIT": "Cloud application closed. Reopening...", "LK_APP_PROCESS_NOTIFI_APP_QUIT_2": "Cloud application closed."}}