@import "~@/assets/styles/mobile_base.scss";
::-webkit-scrollbar {
    width:5px;
    height:5px;
}
::-webkit-scrollbar-thumb {
    background-color:#dddddd;
    background-clip:padding-box;
    min-height:10px;
}
.icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}
html,body {
    width: 100%;
    height: 100%;
}
.el-scrollbar .el-scrollbar__bar {
    opacity: 1 !important;
}
.index {
    position: absolute;
    background-color: var(--webclient-bg-color, rgba(0, 0, 0, 0.05));
    .container {
        margin: 0 auto;
        box-shadow: 0 0 10px rgba(0,0,0,0.4);
        background-color: rgba(0,0,0,0.05);
        position: relative;
        overflow: hidden;
    }
    .inputText {
        // transition: all .5s ease-in-out;
        position: absolute;
        z-index: 1029;
        background-color: rgba(68, 68, 68, 0.80);
        // background-color: $color-bg-trans40-blue;
        width: 50%;
        left: 50%;
        transform: translateX(-50%);
        color: #fff;
        font-size: 16px;
        padding: 1rem 1.78rem;
        border-radius: 6px;
        display: flex;
        align-items: center;
        >div {
            width: 100%;
        }
        .title {
            margin-bottom: 5px;
            text-align: left;
        }
        textarea {
            width: 88%;
            height: 3rem;
            border: 0;
            box-shadow: 0 0 black;
            font-size: 14px;
            outline: 0;
            border-radius: 6px;
            box-sizing: border-box;
            padding: 0.56rem 0.89rem;
        }
        .button-return {
            width: 2.46rem;
            padding: 0.7rem 0.5rem;
            cursor: pointer;
        }

        .button-close {
            width: 2.86rem;
            cursor: pointer;
        }
        .closeBtn-box {
            width: 5%;
            height: 25px;
            text-align: right;
        }
        .closeBtn {
            width: 16px;
            height: 16px;
            position: absolute;
            top: 0;
            right: -14px;
        }
    }
    .inputText.enable {
        // bottom: 0;
    }
    .inputText.disable {
        // bottom: -999px;
    }
    .modalInputPanel {
        position: absolute;
        z-index: 1101;
        background-color: $color-bg-trans01-black;
        display: flex;
        justify-content: center;
        align-items: center;
        // width: 100vw;
        // height: 100vh;
        top: 0;
        /* */
        .container {
            width: 400px;
            /* height: 100px; */
            background-color: $color-bg-trans80-blue;
            border: px2rem(2) solid $color-border-trans80-light-blue;
            border-radius: 2px;
            word-break: break-word;
            color: white;
            padding: 10px;
            overflow: auto;
            word-break: break-word;
            .info {
                margin: 10px 0;
            }
            .info-row {
                margin-bottom: 10px;
                padding: 0 30px;
            }
            &-label {
                font-size: 16px;
                display: inlien-block;
                width: 110px;
            }
            .inputContainer {
                padding: 0 10px;
                width: calc(100% - 110px);
                input {
                    width: 100%;
                    outline: none;
                    padding: 5px;
                }
            }
            .info {
                text-align: center;
                h1 {
                    font-size: 20px;
                    margin: 1rem;
                }
                input.warning {
                    border: 1px solid red;
                }
            }
        }
    }
}
.loading.svelte-ro1nws.svelte-ro1nws{
    background-image: url('@/assets/images/loader.svg'), linear-gradient(135deg, #0a2463 0%, #3e92cc 100%) !important;
    background-size: 120px auto, 100% 100% !important;
    background-repeat: no-repeat !important;
    background-position: center, center !important;
}


.resource-full-message {
    display: none !important;
  }